class Hammam {
  final int? id;
  final String name;
  final String? ownerName;
  final int? rating;
  final double? xposition;
  final double? yposition;

  Hammam({
    this.id,
    required this.name,
    this.ownerName,
    this.rating,
    this.xposition,
    this.yposition,
  });

  factory Hammam.fromJson(Map<String, dynamic> json) {
    return Hammam(
      id: json['id'],
      name: json['name'],
      ownerName: json['owner_name'],
      rating: json['rating'],
      xposition: json['xposition']?.toDouble(),
      yposition: json['yposition']?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'owner_name': ownerName,
      'rating': rating,
      'xposition': xposition,
      'yposition': yposition,
    };
  }

  @override
  String toString() {
    return 'Hammam{id: $id, name: $name, ownerName: $ownerName, rating: $rating, xposition: $xposition, yposition: $yposition}';
  }
}
