import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:logger/logger.dart';
import '../../data/models/user.dart';

class StorageService {
  static const String _userKey = 'user_data';
  static const String _isLoggedInKey = 'is_logged_in';
  static const String _tokenKey = 'auth_token';

  static SharedPreferences? _prefs;
  static final Logger _logger = Logger();

  // Initialize SharedPreferences
  static Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }

  // Save user data after successful login
  static Future<bool> saveUser(User user) async {
    try {
      if (_prefs == null) await init();

      final userJson = jsonEncode(user.toJson());
      await _prefs!.setString(_userKey, userJson);
      await _prefs!.setBool(_isLoggedInKey, true);

      return true;
    } catch (e) {
      _logger.e('Error saving user: $e');
      return false;
    }
  }

  // Get saved user data
  static Future<User?> getUser() async {
    try {
      if (_prefs == null) await init();

      final userJson = _prefs!.getString(_userKey);
      if (userJson != null) {
        final userMap = jsonDecode(userJson) as Map<String, dynamic>;
        return User.fromJson(userMap);
      }
      return null;
    } catch (e) {
      _logger.e('Error getting user: $e');
      return null;
    }
  }

  // Check if user is logged in
  static Future<bool> isLoggedIn() async {
    try {
      if (_prefs == null) await init();
      return _prefs!.getBool(_isLoggedInKey) ?? false;
    } catch (e) {
      _logger.e('Error checking login status: $e');
      return false;
    }
  }

  // Save authentication token (if needed)
  static Future<bool> saveToken(String token) async {
    try {
      if (_prefs == null) await init();
      await _prefs!.setString(_tokenKey, token);
      return true;
    } catch (e) {
      _logger.e('Error saving token: $e');
      return false;
    }
  }

  // Get authentication token
  static Future<String?> getToken() async {
    try {
      if (_prefs == null) await init();
      return _prefs!.getString(_tokenKey);
    } catch (e) {
      _logger.e('Error getting token: $e');
      return null;
    }
  }

  // Clear all user data (logout)
  static Future<bool> clearUserData() async {
    try {
      if (_prefs == null) await init();

      await _prefs!.remove(_userKey);
      await _prefs!.remove(_isLoggedInKey);
      await _prefs!.remove(_tokenKey);

      return true;
    } catch (e) {
      _logger.e('Error clearing user data: $e');
      return false;
    }
  }

  // Clear all stored data
  static Future<bool> clearAll() async {
    try {
      if (_prefs == null) await init();
      await _prefs!.clear();
      return true;
    } catch (e) {
      _logger.e('Error clearing all data: $e');
      return false;
    }
  }

  // Save any string value
  static Future<bool> saveString(String key, String value) async {
    try {
      if (_prefs == null) await init();
      await _prefs!.setString(key, value);
      return true;
    } catch (e) {
      _logger.e('Error saving string: $e');
      return false;
    }
  }

  // Get any string value
  static Future<String?> getString(String key) async {
    try {
      if (_prefs == null) await init();
      return _prefs!.getString(key);
    } catch (e) {
      _logger.e('Error getting string: $e');
      return null;
    }
  }

  // Save any boolean value
  static Future<bool> saveBool(String key, bool value) async {
    try {
      if (_prefs == null) await init();
      await _prefs!.setBool(key, value);
      return true;
    } catch (e) {
      _logger.e('Error saving bool: $e');
      return false;
    }
  }

  // Get any boolean value
  static Future<bool?> getBool(String key) async {
    try {
      if (_prefs == null) await init();
      return _prefs!.getBool(key);
    } catch (e) {
      _logger.e('Error getting bool: $e');
      return null;
    }
  }
}
