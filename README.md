# Mila Tour App

تطبيق سياحي لاستكشاف المرافق السياحية في منطقة ميلة، مطور بـ Flutter مع دعم كامل للغة العربية.

## الميزات الرئيسية

### 🔐 نظام المصادقة
- تسجيل الدخول الآمن
- **حفظ بيانات المستخدم محلياً** - لا حاجة لتسجيل الدخول مرة أخرى
- تسجيل الخروج مع تأكيد
- شاشة splash ذكية للتحقق من حالة تسجيل الدخول

### 🗺️ الخرائط التفاعلية
- خرائط تفاعلية باستخدام OpenStreetMap
- عرض المرافق السياحية على الخريطة
- تنقل سهل وسلس

### 🏨 المرافق السياحية
- **8 أنواع من المرافق:**
  - الفنادق (Hotels)
  - النزل (Auberge)
  - الحمامات (Hammam)
  - المنابع (Manbaa)
  - الجمعيات (Associations)
  - المعالم السياحية (Tourist Attractions)
  - المرافق التقليدية (Traditional)
  - وكالات السفر (Travel Agencies)

### 📱 واجهة المستخدم
- تصميم عصري ومتجاوب
- دعم كامل للغة العربية
- ألوان متناسقة وجذابة
- تنقل سهل بين الشاشات

## التقنيات المستخدمة

- **Flutter** - إطار العمل الرئيسي
- **GetX** - إدارة الحالة والتوجيه
- **SharedPreferences** - التخزين المحلي
- **Flutter Map** - الخرائط التفاعلية
- **HTTP** - طلبات الشبكة
- **Logger** - تسجيل الأحداث

## البنية المعمارية

```
lib/
├── core/                    # الوظائف الأساسية
│   ├── api/                # إعدادات API
│   ├── constants/          # الثوابت والألوان
│   ├── di/                 # حقن التبعيات
│   ├── routes/             # نظام التوجيه
│   ├── services/           # الخدمات المشتركة
│   └── widgets/            # الويدجت المشتركة
├── data/                   # طبقة البيانات
│   ├── models/             # نماذج البيانات
│   ├── repositories/       # المستودعات
│   └── services/           # خدمات API
├── presentation/           # طبقة العرض
│   ├── authentication/     # شاشات المصادقة
│   ├── facilities/         # شاشات المرافق
│   ├── home/              # الشاشة الرئيسية
│   ├── splash/            # شاشة البداية
│   └── map/               # شاشات الخريطة
└── main.dart              # نقطة البداية
```

## الميزات الجديدة المضافة

### 💾 التخزين المحلي للمستخدم
- **خدمة التخزين (`StorageService`)**: إدارة شاملة للتخزين المحلي
- **حفظ بيانات المستخدم**: تلقائياً بعد تسجيل الدخول الناجح
- **التحقق من حالة تسجيل الدخول**: عند بدء التطبيق
- **شاشة Splash ذكية**: تتحقق من حالة المستخدم وتوجهه للشاشة المناسبة
- **تسجيل خروج آمن**: مع حذف جميع البيانات المحلية

### 🔄 تدفق التطبيق الجديد
1. **شاشة Splash** - التحقق من حالة تسجيل الدخول
2. **إذا كان مسجل دخول** → الشاشة الرئيسية مباشرة
3. **إذا لم يكن مسجل دخول** → شاشة تسجيل الدخول
4. **بعد تسجيل الدخول** → حفظ البيانات + الانتقال للشاشة الرئيسية
5. **تسجيل الخروج** → حذف البيانات + العودة لشاشة تسجيل الدخول

## التشغيل

```bash
# تثبيت التبعيات
flutter pub get

# تشغيل التطبيق
flutter run
```

## المتطلبات

- Flutter SDK ^3.8.0
- Dart SDK
- Android Studio / VS Code
- جهاز Android أو iOS للاختبار

## الإعدادات

تأكد من تحديث ملف `lib/core/api/api_config.dart` بعناوين API الصحيحة قبل التشغيل.

## المساهمة

نرحب بالمساهمات! يرجى إنشاء Pull Request أو فتح Issue للمناقشة.

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.
