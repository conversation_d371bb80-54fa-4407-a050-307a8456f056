import 'dart:convert';

class TravelAgency {
  final int? id;
  final String name;
  final String? ownerName;
  final String? managerName;
  final String? address;
  final String? fax;
  final int? rating;
  final double? xposition;
  final double? yposition;

  TravelAgency({
    this.id,
    required this.name,
    this.ownerName,
    this.managerName,
    this.address,
    this.fax,
    this.rating,
    this.xposition,
    this.yposition,
  });

  factory TravelAgency.fromJson(Map<String, dynamic> json) {
    return TravelAgency(
      id: json['id'],
      name: json['name'],
      ownerName: json['owner_name'],
      managerName: json['manager_name'],
      address: json['address'],
      fax: json['fax'],
      rating: json['rating'],
      xposition: json['xposition']?.toDouble(),
      yposition: json['yposition']?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'owner_name': owner<PERSON><PERSON>,
      'manager_name': managerName,
      'address': address,
      'fax': fax,
      'rating': rating,
      'xposition': xposition,
      'yposition': yposition,
    };
  }

  @override
  String toString() {
    return 'TravelAgency{id: $id, name: $name, ownerName: $ownerName, managerName: $managerName, address: $address, fax: $fax, rating: $rating, xposition: $xposition, yposition: $yposition}';
  }
}
