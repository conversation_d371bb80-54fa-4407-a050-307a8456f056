import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:mila_tour_app/data/models/api_response.dart'; // Import ApiResponse
import 'package:mila_tour_app/data/models/user.dart';
import 'package:mila_tour_app/data/repositories/user_repo.dart';
import 'package:mila_tour_app/core/services/storage_service.dart';

class LoginController extends GetxController {
  final UserRepository userRepository;
  final Logger _logger = Logger();

  LoginController({required this.userRepository});

  // Observable variables
  final isLoading = false.obs;
  final errorMessage = RxString('');
  final user = Rx<User?>(null);

  // Login method
  Future<void> login(String email, String password) async {
    isLoading.value = true;
    errorMessage.value = '';

    final ApiResponse<User> response = await userRepository.login(
      email,
      password,
    );
    isLoading.value = false;

    if (response.success && response.data != null) {
      user.value = response.data;

      // Save user data to local storage
      final saveSuccess = await StorageService.saveUser(response.data!);
      if (!saveSuccess) {
        _logger.w('Warning: Failed to save user data locally');
      }

      // Show success message from API or a default one
      Get.snackbar(
        'نجاح',
        response.message ??
            'تم تسجيل الدخول بنجاح! مرحباً ${response.data!.firstname}',
        snackPosition: SnackPosition.BOTTOM,
      );
      // Navigate to home screen
      Get.offAllNamed('/home');
    } else {
      // Show error message from API or a default one
      String message = response.message ?? 'حدث خطأ غير معروف.';
      errorMessage.value = message;
      Get.snackbar(
        'خطأ',
        'خطأ في تسجيل الدخول: $message',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Get.theme.colorScheme.error.withValues(alpha: 0.1),
        colorText: Get.theme.colorScheme.error,
      );
    }
  }

  // Logout method
  Future<void> logout() async {
    isLoading.value = true;

    // Clear local storage
    final clearSuccess = await StorageService.clearUserData();
    if (clearSuccess) {
      user.value = null;
      errorMessage.value = '';

      Get.snackbar(
        'تم تسجيل الخروج',
        'تم تسجيل الخروج بنجاح',
        snackPosition: SnackPosition.BOTTOM,
      );

      // Navigate to login screen
      Get.offAllNamed('/login');
    } else {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تسجيل الخروج',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Get.theme.colorScheme.error.withValues(alpha: 0.1),
        colorText: Get.theme.colorScheme.error,
      );
    }

    isLoading.value = false;
  }

  // Check if user is already logged in
  Future<void> checkLoginStatus() async {
    isLoading.value = true;

    final isLoggedIn = await StorageService.isLoggedIn();
    if (isLoggedIn) {
      final savedUser = await StorageService.getUser();
      if (savedUser != null) {
        user.value = savedUser;
        // User is already logged in, navigate to home
        Get.offAllNamed('/home');
      } else {
        // Clear invalid data
        await StorageService.clearUserData();
      }
    }

    isLoading.value = false;
  }
}
