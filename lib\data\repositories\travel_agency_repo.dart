import 'dart:convert';
import '../models/travel_agency.dart';
import '../services/travel_agency_service.dart';

class TravelAgencyRepository {
  final TravelAgencyApiService travelAgencyApiService;

  TravelAgencyRepository({required this.travelAgencyApiService});

  Future<List<TravelAgency>> getTravelAgencies() async {
    try {
      final jsonResponse = jsonDecode(
        await travelAgencyApiService.getTravelAgencies(),
      );
      final data = jsonResponse['data'] as List;
      return data.map((item) => TravelAgency.fromJson(item)).toList();
    } catch (e) {
      throw Exception('Failed to get travel agencies: $e');
    }
  }
}
