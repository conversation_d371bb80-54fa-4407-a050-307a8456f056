import 'dart:convert';
import '../models/traditional.dart';
import '../services/traditional_service.dart';

class TraditionalRepository {
  final TraditionalApiService traditionalApiService;

  TraditionalRepository({required this.traditionalApiService});

  Future<List<Traditional>> getTraditionals() async {
    try {
      final jsonResponse = jsonDecode(
        await traditionalApiService.getTraditionals(),
      );
      final data = jsonResponse['data'] as List;
      return data.map((item) => Traditional.fromJson(item)).toList();
    } catch (e) {
      throw Exception('Failed to get traditionals: $e');
    }
  }
}
