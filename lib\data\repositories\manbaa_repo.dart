import 'dart:convert';
import '../models/manbaa.dart';
import '../services/manbaa_service.dart';

class ManbaaRepository {
  final ManbaaApiService manbaaApiService;

  ManbaaRepository({required this.manbaaApiService});

  Future<List<Manbaa>> getManbaas() async {
    try {
      final jsonResponse = jsonDecode(await manbaaApiService.getManbaas());
      final data = jsonResponse['data'] as List;
      return data.map((item) => Manbaa.fromJson(item)).toList();
    } catch (e) {
      throw Exception('Failed to get manbaas: $e');
    }
  }
}
