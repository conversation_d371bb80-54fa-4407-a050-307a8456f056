# دليل اختبار ميزة خريطة الأماكن

## نظرة عامة

هذا الدليل يوضح كيفية اختبار ميزة خريطة الأماكن التفاعلية الجديدة في تطبيق Mila Tour.

## متطلبات الاختبار

### البيانات المطلوبة
تأكد من أن قاعدة البيانات تحتوي على مرافق مع إحداثيات صحيحة:

```sql
-- مثال على بيانات الفنادق مع الإحداثيات
INSERT INTO hotels (name, address, rating, xposition, yposition) VALUES 
('فندق ميلة الكبير', 'وسط المدينة', 4, 6.2648, 36.4503),
('فندق النجمة', 'شارع الاستقلال', 3, 6.2700, 36.4550);

-- مثال على بيانات الحمامات
INSERT INTO hammam (name, owner_name, rating, xposition, yposition) VALUES 
('حمام الشفاء', 'أحمد محمد', 5, 6.2600, 36.4480),
('حمام الصحة', 'فاطمة علي', 4, 6.2750, 36.4600);
```

### إعداد البيئة
1. تأكد من تشغيل الخادم المحلي
2. تحديث `ApiConfig` بعناوين API الصحيحة
3. اتصال بالإنترنت لتحميل خرائط OpenStreetMap

## سيناريوهات الاختبار

### 1. اختبار التحميل الأولي

**الخطوات:**
1. فتح التطبيق
2. تسجيل الدخول (إذا لم يكن مسجل دخول)
3. الانتقال لتبويب "Places" في الشريط السفلي
4. انتظار تحميل الخريطة

**النتائج المتوقعة:**
- ✅ ظهور شاشة تحميل مع رسالة "جاري تحميل الأماكن..."
- ✅ ظهور الخريطة مع التركيز على مركز ميلة
- ✅ ظهور مؤشرات ملونة للمرافق المختلفة
- ✅ ظهور أزرار التصفية في الأعلى
- ✅ ظهور عداد المواقع في الأعلى يميناً

### 2. اختبار المؤشرات الملونة

**الخطوات:**
1. فحص المؤشرات على الخريطة
2. التحقق من الألوان والأيقونات

**النتائج المتوقعة:**
- 🔴 **أحمر** - الفنادق (أيقونة فندق)
- 🟠 **برتقالي** - الحمامات (أيقونة حوض استحمام)
- 🔵 **أزرق** - الجمعيات (أيقونة مجموعات)
- 🟢 **أخضر** - النزل (أيقونة منزل)
- 🟣 **بنفسجي** - المعالم السياحية (أيقونة معالم)
- 🟤 **بني** - المرافق التقليدية (أيقونة متحف)
- 🩷 **وردي** - وكالات السفر (أيقونة طائرة)
- 🔷 **سماوي** - المنابع (أيقونة قطرة ماء)

### 3. اختبار التصفية

**الخطوات:**
1. النقر على زر "الكل" - يجب عرض جميع المرافق
2. النقر على "الفنادق" - يجب عرض الفنادق فقط
3. النقر على "الحمامات" - يجب عرض الحمامات فقط
4. تكرار مع باقي الأنواع

**النتائج المتوقعة:**
- ✅ تحديث المؤشرات فوراً عند التصفية
- ✅ تحديث عداد المواقع ليعكس العدد الصحيح
- ✅ تمييز زر التصفية المختار بلون مختلف
- ✅ إخفاء المؤشرات غير المطلوبة

### 4. اختبار التفاعل مع المؤشرات

**الخطوات:**
1. النقر على أي مؤشر على الخريطة
2. فحص النافذة المنبثقة

**النتائج المتوقعة:**
- ✅ ظهور نافذة منبثقة من الأسفل
- ✅ عرض اسم المرفق ونوعه
- ✅ عرض التقييم (إذا متوفر)
- ✅ وجود زر "عرض التفاصيل"
- ✅ وجود زر "التركيز على الموقع"
- ✅ إمكانية إغلاق النافذة بالسحب لأسفل

### 5. اختبار أدوات التحكم

**الخطوات:**
1. النقر على زر "+" للتكبير
2. النقر على زر "-" للتصغير
3. النقر على أيقونة الموقع في شريط التطبيق

**النتائج المتوقعة:**
- ✅ تكبير الخريطة عند النقر على "+"
- ✅ تصغير الخريطة عند النقر على "-"
- ✅ العودة لمركز ميلة عند النقر على أيقونة الموقع
- ✅ تحديث سلس بدون تقطع

### 6. اختبار دليل الألوان

**الخطوات:**
1. فحص دليل الألوان في الأسفل يساراً
2. مقارنة الألوان مع المؤشرات

**النتائج المتوقعة:**
- ✅ ظهور دليل الألوان بوضوح
- ✅ تطابق الألوان مع المؤشرات الفعلية
- ✅ وضوح النصوص والأيقونات

### 7. اختبار معالجة الأخطاء

**الخطوات:**
1. قطع الاتصال بالإنترنت
2. إعادة فتح الشاشة
3. أو تعديل عنوان API لعنوان خاطئ

**النتائج المتوقعة:**
- ✅ ظهور رسالة خطأ واضحة
- ✅ وجود زر "إعادة المحاولة"
- ✅ عدم تعطل التطبيق

### 8. اختبار الأداء

**الخطوات:**
1. تحميل عدد كبير من المرافق (100+ مرفق)
2. التنقل بين التصفيات بسرعة
3. التكبير والتصغير المتكرر

**النتائج المتوقعة:**
- ✅ تحميل سلس بدون تأخير ملحوظ
- ✅ تصفية سريعة ومتجاوبة
- ✅ عدم تجمد الواجهة

## اختبارات الحالات الحدية

### 1. مرافق بدون إحداثيات
**السيناريو:** مرافق في قاعدة البيانات بدون `xposition` أو `yposition`
**النتيجة المتوقعة:** عدم ظهور مؤشر لهذه المرافق

### 2. إحداثيات خاطئة
**السيناريو:** إحداثيات خارج منطقة ميلة
**النتيجة المتوقعة:** ظهور المؤشر في الموقع المحدد (حتى لو بعيد)

### 3. عدم وجود مرافق
**السيناريو:** قاعدة بيانات فارغة أو عدم وجود مرافق لنوع معين
**النتيجة المتوقعة:** خريطة فارغة مع رسالة مناسبة

### 4. شبكة بطيئة
**السيناريو:** اتصال إنترنت بطيء
**النتيجة المتوقعة:** ظهور مؤشر تحميل لفترة أطول

## تقرير الأخطاء

### معلومات مطلوبة عند الإبلاغ عن خطأ:
1. **نوع الجهاز:** Android/iOS
2. **إصدار النظام:** مثل Android 11
3. **خطوات إعادة الإنتاج:** تفصيلية
4. **النتيجة المتوقعة:** ما كان يجب أن يحدث
5. **النتيجة الفعلية:** ما حدث بالفعل
6. **لقطات شاشة:** إذا أمكن
7. **رسائل الخطأ:** من console أو logs

### أخطاء شائعة وحلولها:

**1. عدم ظهور المؤشرات:**
- تحقق من وجود إحداثيات في قاعدة البيانات
- تحقق من اتصال API

**2. خريطة فارغة:**
- تحقق من اتصال الإنترنت
- تحقق من عمل OpenStreetMap

**3. تطبيق بطيء:**
- تحقق من عدد المرافق في قاعدة البيانات
- تحقق من سرعة الشبكة

**4. أزرار التصفية لا تعمل:**
- تحقق من console للأخطاء
- إعادة تشغيل التطبيق

## الخلاصة

اتبع هذا الدليل خطوة بخطوة للتأكد من عمل جميع ميزات خريطة الأماكن بشكل صحيح. في حالة وجود أي مشاكل، راجع قسم "تقرير الأخطاء" وقم بتوثيق المشكلة بالتفصيل.
