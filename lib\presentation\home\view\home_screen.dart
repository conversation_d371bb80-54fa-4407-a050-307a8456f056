import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart'; // Import flutter_map
import 'package:latlong2/latlong.dart'; // Import latlong2 for coordinates
import 'package:get/get.dart';
import 'package:mila_tour_app/core/widgets/custom_text_field.dart';
import 'package:mila_tour_app/core/widgets/transport_option_button.dart';
import 'package:mila_tour_app/presentation/authentication/login/controller/login_controller.dart';
import '../../../core/constants/app_colors.dart';
import 'widgets/search_section.dart'; // Import new widget
import 'widgets/transport_options_grid.dart'; // Import new widget
import 'widgets/location_banner.dart'; // Import new widget

class HomeScreen extends StatefulWidget {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _currentIndex = 0;

  // Keep track of the selected bottom nav index
  // final List<Widget> _screens = [ ... ]; // We'll replace this logic

  // Define map controller if needed for interactions
  final MapController _mapController = MapController();

  // Show logout confirmation dialog
  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('تسجيل الخروج'),
          content: const Text('هل أنت متأكد من أنك تريد تسجيل الخروج؟'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog
              },
              child: const Text('إلغاء'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog
                _logout(); // Perform logout
              },
              child: const Text(
                'تسجيل الخروج',
                style: TextStyle(color: Colors.red),
              ),
            ),
          ],
        );
      },
    );
  }

  // Perform logout
  void _logout() {
    final loginController = Get.find<LoginController>();
    loginController.logout();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // We only need the map and overlay for the main home view (index 0)
      // Other tabs might have different bodies
      body: IndexedStack(
        index: _currentIndex,
        children: [
          _buildHomeContent(), // Main home screen with map
          const Center(child: Text('Work Screen Placeholder')),
          const Center(child: Text('Places Screen Placeholder')),
          const Center(child: Text('Profile Screen Placeholder')),
        ],
      ),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.2),
              spreadRadius: 1,
              blurRadius: 8,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: BottomNavigationBar(
          currentIndex: _currentIndex,
          onTap: (index) => setState(() => _currentIndex = index),
          selectedItemColor: AppColors.primary,
          unselectedItemColor: Colors.grey,
          type: BottomNavigationBarType.fixed,
          backgroundColor: Colors.transparent,
          elevation: 0,
          items: const [
            BottomNavigationBarItem(icon: Icon(Icons.home), label: 'Home'),
            BottomNavigationBarItem(icon: Icon(Icons.work), label: 'Work'),
            BottomNavigationBarItem(icon: Icon(Icons.place), label: 'Places'),
            BottomNavigationBarItem(icon: Icon(Icons.person), label: 'Profile'),
          ],
        ),
      ),
    );
  }

  // New method to build the home screen content with map and overlays
  Widget _buildHomeContent() {
    return Stack(
      children: [
        // Map Layer (using flutter_map)
        FlutterMap(
          mapController: _mapController,
          options: MapOptions(
            initialCenter: LatLng(51.509865, -0.118092), // Example: London
            initialZoom: 13.0,
          ),
          children: [
            TileLayer(
              urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
              userAgentPackageName:
                  'com.example.app', // Replace with your app's package name
            ),
            // TODO: Add markers based on the image (S, D, R, F, H, L, M, etc.)
            // MarkerLayer(...)
          ],
        ),

        // UI Overlay Layer
        Positioned(
          top: 40, // Adjust as needed for status bar
          left: 15,
          child: CircleAvatar(
            backgroundColor: Colors.white,
            child: IconButton(
              icon: const Icon(Icons.logout, color: Colors.red),
              onPressed: () {
                _showLogoutDialog();
              },
            ),
          ),
        ),
        Positioned(
          top: 40, // Adjust as needed
          right: 15,
          child: CircleAvatar(
            backgroundColor: Colors.white,
            child: IconButton(
              icon: const Icon(Icons.notifications_none, color: Colors.black54),
              onPressed: () {
                /* TODO: Notifications action */
              },
            ),
          ),
        ),

        // Bottom Sheet like UI
        Positioned(
          left: 0,
          right: 0,
          bottom: 0,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                decoration: const BoxDecoration(
                  color: Colors.white, // Background for search section
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(20.0),
                    topRight: Radius.circular(20.0),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black12,
                      blurRadius: 10,
                      offset: Offset(0, -2),
                    ),
                  ],
                ),
                child: const SearchSection(),
              ),
              const TransportOptionsGrid(),
              const LocationBanner(),
              // Add some padding at the very bottom if needed, or let the BottomNavBar handle it
              // SizedBox(height: MediaQuery.of(context).padding.bottom),
            ],
          ),
        ),
      ],
    );
  }
}

class HomeContent extends StatelessWidget {
  const HomeContent({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                Row(
                  children: [
                    const CircleAvatar(
                      backgroundColor: Colors.grey,
                      child: Icon(Icons.settings, color: Colors.white),
                    ),
                    const Spacer(),
                    IconButton(
                      icon: const Icon(Icons.notifications),
                      onPressed: () {},
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                CustomTextField(
                  hintText: 'Get Me Somewhere',
                  prefixIcon: const Icon(Icons.search),
                  readOnly: true,
                  onTap: () {},
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: CustomTextField(
                        hintText: 'Get Me Home',
                        prefixIcon: const Icon(Icons.home),
                        readOnly: true,
                        onTap: () {},
                      ),
                    ),
                    const SizedBox(width: 12),
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.grey.withOpacity(0.1),
                            spreadRadius: 1,
                            blurRadius: 4,
                            offset: const Offset(0, 1),
                          ),
                        ],
                      ),
                      child: IconButton(
                        icon: const Icon(Icons.work),
                        onPressed: () {},
                      ),
                    ),
                    const SizedBox(width: 12),
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.grey.withOpacity(0.1),
                            spreadRadius: 1,
                            blurRadius: 4,
                            offset: const Offset(0, 1),
                          ),
                        ],
                      ),
                      child: IconButton(
                        icon: const Icon(Icons.star),
                        onPressed: () {},
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),
          Container(
            height: 100,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: ListView(
              scrollDirection: Axis.horizontal,
              children: [
                TransportOptionButton(
                  label: 'All',
                  icon: Icons.location_on,
                  onTap: () {},
                  isSelected: true,
                ),
                const SizedBox(width: 12),
                TransportOptionButton(
                  label: 'Hammam',
                  icon: Icons.hot_tub,
                  onTap: () {},
                ),
                const SizedBox(width: 12),
                TransportOptionButton(
                  label: 'Hotel',
                  icon: Icons.hotel,
                  onTap: () {},
                ),
                const SizedBox(width: 12),
                TransportOptionButton(
                  label: 'Association',
                  icon: Icons.groups,
                  onTap: () {},
                ),
                const SizedBox(width: 12),
                TransportOptionButton(
                  label: 'Manbaa',
                  icon: Icons.water_drop,
                  onTap: () {},
                ),
                const SizedBox(width: 12),
                TransportOptionButton(
                  label: 'Auberge',
                  icon: Icons.house,
                  onTap: () {},
                ),
                const SizedBox(width: 12),
                TransportOptionButton(
                  label: 'Attractions',
                  icon: Icons.attractions,
                  onTap: () {},
                ),
                const SizedBox(width: 12),
                TransportOptionButton(
                  label: 'Restaurant',
                  icon: Icons.restaurant,
                  onTap: () {},
                ),
                const SizedBox(width: 12),
                TransportOptionButton(
                  label: 'Museum',
                  icon: Icons.museum,
                  onTap: () {},
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
