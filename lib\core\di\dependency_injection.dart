import 'package:get/get.dart';
import 'package:mila_tour_app/data/repositories/user_repo.dart';
import 'package:mila_tour_app/data/services/user_service.dart';
import 'package:mila_tour_app/presentation/authentication/login/controller/login_controller.dart';
import 'package:mila_tour_app/presentation/facilities/controller/facilities_controller.dart';
import 'package:mila_tour_app/presentation/map/controller/map_controller.dart';

// final getIt = GetIt.instance; // Removed GetIt

class DependencyInjection {
  static Future<void> init() async {
    // Register services
    Get.lazyPut<UserApiService>(() => UserApiService(), fenix: true);

    // Register repositories
    Get.lazyPut<UserRepository>(
      () => UserRepository(userApiService: Get.find<UserApiService>()),
      fenix: true,
    );

    // Register use cases

    // Register GetX controllers
    Get.lazyPut<LoginController>(
      () => LoginController(userRepository: Get.find<UserRepository>()),
      fenix: true,
    );

    // Register Facilities controller
    Get.lazyPut<FacilitiesController>(
      () => FacilitiesController(),
      fenix: true,
    );

    // Register Map controller
    Get.lazyPut<MapViewController>(() => MapViewController(), fenix: true);

    // Initialize GetX controllers - No longer needed here as they are lazy loaded
    // Get.put(getIt<LoginController>()); // Removed GetIt initialization
  }
}
