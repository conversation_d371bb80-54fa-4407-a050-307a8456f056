import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mila_tour_app/presentation/authentication/login/controller/login_controller.dart';

class LoginScreen extends StatelessWidget {
  // Changed from StatefulWidget
  LoginScreen({Key? key}) : super(key: key);

  // Moved controllers, form key, and controller instance here
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  final LoginController _loginController = Get.find<LoginController>();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('تسجيل الدخول'), centerTitle: true),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Form(
          key: _formKey,
          child: ListView(
            shrinkWrap: true,
            children: [
              const Text(
                'مرحبا بك',
                style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 30),
              Container(
                height: 200,
                child: const Image(image: AssetImage("assets/logo.jpg")),
              ),
              const SizedBox(height: 30),
              TextFormField(
                controller: _emailController,
                decoration: const InputDecoration(
                  labelText: 'البريد الإلكتروني',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.email),
                ),
                keyboardType: TextInputType.emailAddress,
                textDirection: TextDirection.ltr, // Usually LTR for email
                textAlign: TextAlign.right, // Align text to the right
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'الرجاء إدخال البريد الإلكتروني';
                  }
                  if (!RegExp(r'^[^@]+@[^@]+\.[^@]+').hasMatch(value)) {
                    return 'الرجاء إدخال بريد إلكتروني صحيح';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 20),
              TextFormField(
                controller: _passwordController,
                decoration: const InputDecoration(
                  labelText: 'كلمة المرور',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.lock),
                ),
                obscureText: true,
                textDirection: TextDirection.ltr, // Usually LTR for password
                textAlign: TextAlign.right, // Align text to the right
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'الرجاء إدخال كلمة المرور';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 30),
              Obx(
                () =>
                    _loginController.isLoading.value
                        ? const Center(child: CircularProgressIndicator())
                        : ElevatedButton(
                          onPressed: () {
                            if (_formKey.currentState!.validate()) {
                              final email = _emailController.text;
                              final password = _passwordController.text;
                              // Dispose controllers after use if needed, though GetX controller is better
                              // _emailController.dispose();
                              // _passwordController.dispose();
                              _loginController.login(email, password);
                            }
                          },
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 15),
                          ),
                          child: const Text(
                            'تسجيل الدخول',
                            style: TextStyle(fontSize: 16),
                          ),
                        ),
              ),
              const SizedBox(height: 15),
              TextButton(
                onPressed: () {
                  // Navigate to Signup Screen using GetX
                  // Get.toNamed('/signup');
                },
                child: const Text('ليس لديك حساب؟ إنشاء حساب جديد'),
              ),
              Obx(() {
                if (_loginController.errorMessage.value.isNotEmpty) {
                  // Schedule the dialog to show after the current frame
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    showDialog(
                      context: context,
                      builder: (BuildContext context) {
                        return AlertDialog(
                          title: const Text('خطأ في تسجيل الدخول'),
                          content: Text(_loginController.errorMessage.value),
                          actions: [
                            TextButton(
                              onPressed: () {
                                Navigator.of(context).pop();
                                _loginController.errorMessage.value = '';
                              },
                              child: const Text('حسناً'),
                            ),
                          ],
                        );
                      },
                    );
                  });
                }
                return const SizedBox.shrink();
              }),
            ],
          ),
        ),
      ),
    );
  }
}
