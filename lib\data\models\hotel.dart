class Hotel {
  final int? id;
  final String name;
  final String? address;
  final int? rating;
  final String? theme;
  final String? telephone;
  final String? fax;
  final List<String> images;
  final double? xposition;
  final double? yposition;

  Hotel({
    this.id,
    required this.name,
    this.address,
    this.rating,
    this.theme,
    this.telephone,
    this.fax,
    this.images = const [],
    this.xposition,
    this.yposition,
  });

  factory Hotel.fromJson(Map<String, dynamic> json) {
    return Hotel(
      id: json['id'],
      name: json['name'],
      address: json['address'],
      rating: json['rating'],
      theme: json['theme'],
      telephone: json['telephone'],
      fax: json['fax'],
      images: json['images'] != null ? List<String>.from(json['images']) : [],
      xposition: json['xposition']?.toDouble(),
      yposition: json['yposition']?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'address': address,
      'rating': rating,
      'theme': theme,
      'telephone': telephone,
      'fax': fax,
      'images': images,
      'xposition': xposition,
      'yposition': yposition,
    };
  }

  @override
  String toString() {
    return 'Hotel{id: $id, name: $name, address: $address, rating: $rating, theme: $theme, telephone: $telephone, fax: $fax, images: $images, xposition: $xposition, yposition: $yposition}';
  }
}
