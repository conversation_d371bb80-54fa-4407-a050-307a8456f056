import 'dart:convert';
import '../models/tourist_attraction.dart';
import '../services/tourist_attraction_service.dart';

class TouristAttractionRepository {
  final TouristAttractionApiService touristAttractionApiService;

  TouristAttractionRepository({required this.touristAttractionApiService});

  Future<List<TouristAttraction>> getTouristAttractions() async {
    try {
      final jsonResponse = jsonDecode(
        await touristAttractionApiService.getTouristAttractions(),
      );
      final data = jsonResponse['data'] as List;
      return data.map((item) => TouristAttraction.fromJson(item)).toList();
    } catch (e) {
      throw Exception('Failed to get tourist attractions: $e');
    }
  }
}
