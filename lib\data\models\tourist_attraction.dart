import 'dart:convert';

class TouristAttraction {
  final int? id;
  final String name;
  final String? description;
  final int? rating;
  final List<String> images;
  final double? xposition;
  final double? yposition;

  TouristAttraction({
    this.id,
    required this.name,
    this.description,
    this.rating,
    this.images = const [],
    this.xposition,
    this.yposition,
  });

  factory TouristAttraction.fromJson(Map<String, dynamic> json) {
    return TouristAttraction(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      rating: json['rating'],
      images: json['images'] != null ? List<String>.from(json['images']) : [],
      xposition: json['xposition']?.toDouble(),
      yposition: json['yposition']?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'rating': rating,
      'images': images,
      'xposition': xposition,
      'yposition': yposition,
    };
  }

  @override
  String toString() {
    return 'TouristAttraction{id: $id, name: $name, description: $description, rating: $rating, images: $images, xposition: $xposition, yposition: $yposition}';
  }
}
