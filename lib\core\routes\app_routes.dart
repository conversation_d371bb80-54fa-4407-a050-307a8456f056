import 'package:get/get.dart';
import 'package:mila_tour_app/presentation/authentication/login/view/login_screen.dart';
import 'package:mila_tour_app/presentation/facilities/view/facilities_grid_screen.dart';
import 'package:mila_tour_app/presentation/home/<USER>/home_screen.dart';
import 'package:mila_tour_app/presentation/map/view/places_map_screen.dart';
import 'package:mila_tour_app/presentation/splash/view/splash_screen.dart';

class AppRoutes {
  static const String initial = '/splash'; // Set initial route to splash
  static const String splash = '/splash';
  static const String login = '/login';
  static const String home = '/home';
  static const String facilities = '/facilities';
  static const String placesMap = '/places-map';

  static final routes = [
    GetPage(
      name: splash,
      page: () => const SplashScreen(),
      transition: Transition.fade,
    ),
    GetPage(
      name: login,
      page: () => LoginScreen(),
      transition: Transition.fade,
    ),
    GetPage(
      name: home,
      page: () => const HomeScreen(),
      transition: Transition.fade,
    ),
    GetPage(
      name: placesMap,
      page: () => const PlacesMapScreen(),
      transition: Transition.fade,
    ),
    GetPage(
      name: '$facilities/:type',
      page: () {
        final String facilityType = Get.parameters['type'] ?? '';
        final String title = _getFacilityTitle(facilityType);
        return FacilitiesGridScreen(facilityType: facilityType, title: title);
      },
      transition: Transition.fade,
    ),
  ];

  // Método auxiliar para obtener el título según el tipo de instalación
  static String _getFacilityTitle(String facilityType) {
    switch (facilityType) {
      case 'associations':
        return 'الجمعيات';
      case 'auberge':
        return 'النزل';
      case 'hammam':
        return 'الحمامات';
      case 'hotel':
        return 'الفنادق';
      case 'manbaa':
        return 'المنابع';
      case 'tourist_attraction':
        return 'المعالم السياحية';
      case 'traditional':
        return 'التقليدية';
      case 'travel_agency':
        return 'وكالات السفر';
      default:
        return 'المنشآت';
    }
  }

  // Configure GetX routes
  static void setupRoutes() {
    Get.config(
      enableLog: true,
      defaultTransition: Transition.fade,
      defaultDurationTransition: const Duration(milliseconds: 250),
    );
  }
}
