class Associations {
  final int? id;
  final String name;
  final String? ownerName;
  final String? address;
  final String? telephone;
  final int? rating;
  final double? xposition;
  final double? yposition;

  Associations({
    this.id,
    required this.name,
    this.ownerName,
    this.address,
    this.telephone,
    this.rating,
    this.xposition,
    this.yposition,
  });

  factory Associations.fromJson(Map<String, dynamic> json) {
    return Associations(
      id: json['id'],
      name: json['name'],
      ownerName: json['owner_name'],
      address: json['address'],
      telephone: json['telephone'],
      rating: json['rating'],
      xposition: json['xposition']?.toDouble(),
      yposition: json['yposition']?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'owner_name': ownerName,
      'address': address,
      'telephone': telephone,
      'rating': rating,
      'xposition': xposition,
      'yposition': yposition,
    };
  }

  @override
  String toString() {
    return 'Associations{id: $id, name: $name, ownerName: $ownerName, address: $address, telephone: $telephone, rating: $rating, xposition: $xposition, yposition: $yposition}';
  }
}
